#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل تطبيق نظام إدارة الإطعام مع معالجة الأخطاء
Run ONOU application with error handling
"""

import sys
import os
import traceback
import logging
from datetime import datetime

# إعداد نظام السجلات
def setup_logging():
    """إعداد نظام السجلات"""
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f"app_{datetime.now().strftime('%Y%m%d')}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return logging.getLogger(__name__)

def check_dependencies():
    """فحص المكتبات المطلوبة"""
    required_modules = [
        'PyQt5',
        'requests', 
        'sqlite3',
        'win32print',
        'zk'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print("✗ المكتبات التالية مفقودة:")
        for module in missing_modules:
            print(f"  - {module}")
        print("\nلتثبيت المكتبات المطلوبة، استخدم:")
        print("pip install -r requirements.txt")
        return False
    
    print("✓ جميع المكتبات المطلوبة متوفرة")
    return True

def check_files():
    """فحص الملفات المطلوبة"""
    required_files = [
        'mm1_clean.py',
        'mm.ui',
        'widget.ui', 
        'db.db',
        'logo.png'
    ]
    
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("✗ الملفات التالية مفقودة:")
        for file in missing_files:
            print(f"  - {file}")
        
        # إنشاء قاعدة البيانات إذا كانت مفقودة
        if 'db.db' in missing_files:
            print("\nمحاولة إنشاء قاعدة البيانات...")
            try:
                from setup_database import main as setup_db
                setup_db()
                missing_files.remove('db.db')
            except Exception as e:
                print(f"فشل في إنشاء قاعدة البيانات: {e}")
        
        if missing_files:
            return False
    
    print("✓ جميع الملفات المطلوبة متوفرة")
    return True

def run_application():
    """تشغيل التطبيق الرئيسي"""
    try:
        # استيراد التطبيق الرئيسي
        from mm1_clean import QApplication, MainWindow
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إنشاء النافذة الرئيسية
        window = MainWindow()
        window.setWindowTitle("نظام إدارة الإطعام - ONOU")
        window.show()
        
        logger.info("تم تشغيل التطبيق بنجاح")
        
        # تشغيل حلقة الأحداث
        sys.exit(app.exec_())
        
    except ImportError as e:
        logger.error(f"خطأ في استيراد المكتبات: {e}")
        print(f"✗ خطأ في استيراد المكتبات: {e}")
        return False
        
    except Exception as e:
        logger.error(f"خطأ في تشغيل التطبيق: {e}")
        logger.error(traceback.format_exc())
        print(f"✗ خطأ في تشغيل التطبيق: {e}")
        return False

def show_help():
    """عرض معلومات المساعدة"""
    help_text = """
نظام إدارة الإطعام في الإقامة الجامعية - ONOU
===============================================

الاستخدام:
  python run_app.py [خيارات]

الخيارات:
  --help, -h        عرض هذه المساعدة
  --check, -c       فحص المتطلبات فقط
  --setup, -s       إعداد قاعدة البيانات
  --test, -t        اختبار الاتصالات
  --version, -v     عرض إصدار التطبيق

أمثلة:
  python run_app.py              # تشغيل التطبيق
  python run_app.py --check      # فحص المتطلبات
  python run_app.py --setup      # إعداد قاعدة البيانات
  python run_app.py --test       # اختبار الاتصالات

للحصول على مزيد من المساعدة، راجع ملف README.md
"""
    print(help_text)

def main():
    """الدالة الرئيسية"""
    global logger
    logger = setup_logging()
    
    # معالجة المعاملات
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        
        if arg in ['--help', '-h']:
            show_help()
            return
            
        elif arg in ['--check', '-c']:
            print("فحص المتطلبات...")
            deps_ok = check_dependencies()
            files_ok = check_files()
            
            if deps_ok and files_ok:
                print("✓ جميع المتطلبات متوفرة")
            else:
                print("✗ بعض المتطلبات مفقودة")
            return
            
        elif arg in ['--setup', '-s']:
            print("إعداد قاعدة البيانات...")
            try:
                from setup_database import main as setup_db
                setup_db()
            except Exception as e:
                print(f"خطأ في إعداد قاعدة البيانات: {e}")
            return
            
        elif arg in ['--test', '-t']:
            print("اختبار الاتصالات...")
            try:
                from test_connection import main as test_conn
                test_conn()
            except Exception as e:
                print(f"خطأ في اختبار الاتصالات: {e}")
            return
            
        elif arg in ['--version', '-v']:
            print("نظام إدارة الإطعام - ONOU v1.0.0")
            return
            
        else:
            print(f"خيار غير معروف: {arg}")
            print("استخدم --help للحصول على المساعدة")
            return
    
    # تشغيل التطبيق العادي
    print("=" * 50)
    print("نظام إدارة الإطعام في الإقامة الجامعية")
    print("ONOU - University Residence Meal Management System")
    print("=" * 50)
    
    logger.info("بدء تشغيل التطبيق")
    
    # فحص المتطلبات
    print("فحص المتطلبات...")
    if not check_dependencies():
        logger.error("فشل في فحص المكتبات المطلوبة")
        return
    
    if not check_files():
        logger.error("فشل في فحص الملفات المطلوبة")
        return
    
    # تشغيل التطبيق
    print("تشغيل التطبيق...")
    run_application()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nتم إيقاف التطبيق بواسطة المستخدم")
    except Exception as e:
        print(f"خطأ غير متوقع: {e}")
        traceback.print_exc()
