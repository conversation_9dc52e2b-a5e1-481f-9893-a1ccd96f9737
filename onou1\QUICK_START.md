# دليل البدء السريع - ONOU

## 🚀 التشغيل السريع

### للمستخدمين العاديين
```bash
start_app.bat
```

### للمطورين
```bash
python run_app.py
```

## 📋 المتطلبات

### تثبيت المكتبات
```bash
pip install -r requirements.txt
```

### المكتبات المطلوبة
- PyQt5==5.15.9
- requests==2.31.0
- pywin32==306
- pyzk==0.9
- sqlite3

## 🔧 الإعداد الأولي

### 1. إعد<PERSON> قاعدة البيانات
```bash
python setup_database.py
```

### 2. اختبار الاتصالات
```bash
python test_connection.py
```

### 3. تشغيل التطبيق
```bash
python mm1_clean.py
```

## 📁 الملفات الرئيسية

- **mm1_clean.py** - الكود المصدري الكامل
- **mm.ui** - واجهة المستخدم الرئيسية
- **widget.ui** - واجهة الإعدادات
- **db.db** - قاعدة البيانات
- **config.py** - إعدادات التطبيق

## 🛠️ أدوات المساعدة

- **setup_database.py** - إعداد قاعدة البيانات
- **test_connection.py** - اختبار الاتصالات
- **run_app.py** - تشغيل مع معالجة الأخطاء
- **start_app.bat** - تشغيل سريع على Windows

## 📖 التوثيق الكامل

راجع الملفات التالية للحصول على معلومات مفصلة:
- **README.md** - دليل المشروع الشامل
- **PROJECT_SUMMARY.md** - ملخص المشروع

## ⚡ نصائح سريعة

1. **للتشغيل الأول**: استخدم `start_app.bat`
2. **لحل المشاكل**: استخدم `test_connection.py`
3. **للتطوير**: ادرس `mm1_clean.py`
4. **للإعدادات**: عدل `config.py`

## 🆘 المساعدة

إذا واجهت مشاكل:
1. تحقق من تثبيت Python 3.8+
2. تأكد من تثبيت جميع المكتبات
3. راجع ملفات السجلات في مجلد `logs/`
4. استخدم أداة اختبار الاتصالات

---
**نظام إدارة الإطعام - ONOU v1.0.0**
