# ملف التكوين للمشروع
# Configuration file for ONOU project

# إعدادات قاعدة البيانات
DATABASE_CONFIG = {
    'name': 'db.db',
    'timeout': 30
}

# إعدادات الشبكة الافتراضية
DEFAULT_NETWORK_CONFIG = {
    'ip': '*************',
    'port': 4370,
    'ip_acc': '*************', 
    'port_acc': 4370,
    'ip_sor': '*************',
    'port_sor': 4370,
    'url': 'http://127.0.0.1:8000'
}

# إعدادات الطباعة
PRINTER_CONFIG = {
    'default_printer': 'POS-80',
    'encoding': 'utf-8',
    'cut_command': '\x1dVB\x00'
}

# إعدادات واجهة المستخدم
UI_CONFIG = {
    'main_ui': 'mm.ui',
    'settings_ui': 'widget.ui',
    'logo': 'logo.png',
    'default_profile': 'profile.jpg',
    'settings_icon': 'set.png'
}

# إعدادات المؤسسة الافتراضية
INSTITUTION_CONFIG = {
    'etab_name': 'الإقامة الجامعية طالب عبد الرحمان',
    'dou_name': 'مديرية الخدمات الجامعية الجزائر غرب'
}

# إعدادات API
API_CONFIG = {
    'attendance_endpoint': '/api/attendance/',
    'accesslog_endpoint': '/api/accesslog/',
    'sortlog_endpoint': '/api/sortlog/',
    'users_endpoint': '/api/users/',
    'photos_endpoint': '/photos/',
    'timeout': 30
}

# إعدادات التطبيق
APP_CONFIG = {
    'title': 'Aissa',
    'version': '1.0.0',
    'author': 'ONOU Development Team',
    'description': 'نظام إدارة الإطعام في الإقامة الجامعية'
}

# رسائل النظام
SYSTEM_MESSAGES = {
    'connection_success': 'تم الاتصال بنجاح',
    'connection_error': 'خطأ في الاتصال',
    'sync_success': 'تم تحديث قاعدة البيانات بنجاح',
    'sync_error': 'خطأ في المزامنة',
    'printer_success': 'تم تحديد الطابعة بنجاح',
    'running_food': 'الطعام قيد التشغيل',
    'running_entry': 'الدخول قيد التشغيل', 
    'running_exit': 'الخروج قيد التشغيل'
}

# إعدادات الأمان
SECURITY_CONFIG = {
    'default_password': '123456',
    'max_retry_attempts': 3,
    'session_timeout': 3600
}
