# uncompyle6 version 3.9.1
# Python bytecode version base 3.8.0 (3413)
# Decompiled from: Python 3.11.5 (tags/v3.11.5:cce6ba9, Aug 24 2023, 14:38:34) [MSC v.1936 64 bit (AMD64)]
# Embedded file name: mm1.py

from PyQt5.QtCore import QThread, pyqtSignal
from PyQt5.QtGui import QIcon, QPixmap
from PyQt5.uic import loadUi
from PyQt5.QtWidgets import QApplication, QMainWindow
import tkinter as tk, time, os, sys, requests, urllib.request, sqlite3, win32print, datetime

CWD = os.path.dirname(os.path.realpath(__file__))
ROOT_DIR = os.path.dirname(CWD)
sys.path.append(ROOT_DIR)
from zk import ZK, const


class Worker(QThread):
    button = pyqtSignal(str)
    button2 = pyqtSignal(str)
    image = pyqtSignal(str)
    meal_num = pyqtSignal(str)
    meal_name = pyqtSignal(str)
    message = pyqtSignal(str)
    message_color = pyqtSignal(str)
    finished = pyqtSignal()
    
    con = sqlite3.connect("db.db")
    printer = con.execute("SELECT value FROM params WHERE params.name = 'printer'")
    printer = printer.fetchone()
    ips = con.execute("SELECT value FROM params WHERE params.name = 'ip'")
    ip = ips.fetchone()
    ports = con.execute("SELECT value FROM params WHERE params.name = 'port'")
    port = ports.fetchone()
    urls = con.execute("SELECT value FROM params WHERE params.name = 'url'")
    url = urls.fetchone()
    con.close()

    def __init__(self):
        super().__init__()
        self.stop_flag = False

    def run(self):
        print("running thread")
        self.finished.emit()
        conn = None
        zk = ZK((self.ip[0]), port=(int(self.port[0])), verbose=True)
        conn = zk.connect()
        
        while True:
            for attendance in conn.live_capture():
                if attendance is None:
                    pass
                else:
                    response = requests.get(self.url[0] + "/api/attendance/" + attendance.user_id)
                    if response.status_code == 200:
                        user = response.json()
                        self.meal_name.emit(str(user["meal"]))
                        self.message.emit(str(user["message"]))
                        self.message_color.emit(str("color: green"))
                        self.meal_num.emit(str(user["meal_sec_num"]))
                        self.button.emit(str(user["name"]))
                        self.button2.emit(str(user["user_id"]))
                        url = self.url[0] + "/photos/" + str(user["photoid"])
                        self.image.emit(url)
                        
                        # Print receipt
                        selected_printer_name = str(self.printer[0])
                        selected_printer = win32print.OpenPrinter(selected_printer_name)
                        print(user["date"])
                        doc_title = "RECEPT"
                        text1 = "             -------------------------------\n"
                        text22 = "             DATE    :      " + str(user["date"]) + "\n"
                        text2 = "             NAME    :      " + str(user["name"]) + "\n"
                        text3 = "             UID     :      " + str(user["user_id"]) + "\n"
                        text4 = "             MEAL    :      " + str(user["meal"]) + "\n"
                        text5 = "             MEALID  :      " + str(user["meal_sec_num"]) + "\n"
                        text6 = "                           ****" + str(user["message"]) + "****\n"
                        cut_command = "\x1dVB\x00"
                        
                        job_info = win32print.StartDocPrinter(selected_printer, 1, (doc_title, None, "RAW"))
                        win32print.StartPagePrinter(selected_printer)
                        win32print.WritePrinter(selected_printer, text3.encode("utf-8"))
                        win32print.WritePrinter(selected_printer, text22.encode("utf-8"))
                        win32print.WritePrinter(selected_printer, text2.encode("utf-8"))
                        win32print.WritePrinter(selected_printer, text4.encode("utf-8"))
                        win32print.WritePrinter(selected_printer, text5.encode("utf-8"))
                        win32print.WritePrinter(selected_printer, text1.encode("utf-8"))
                        win32print.WritePrinter(selected_printer, text6.encode("utf-8"))
                        win32print.WritePrinter(selected_printer, cut_command.encode("utf-8"))
                        win32print.EndPagePrinter(selected_printer)
                        win32print.EndDocPrinter(selected_printer)
                        win32print.ClosePrinter(selected_printer)
                        
                        time.sleep(5)
                        self.finished.emit()
                    else:
                        message = response.json()
                        self.message_color.emit(str("color: red"))
                        self.message.emit(str(message["message"]))
                        time.sleep(5)
                        self.finished.emit()

    def stop(self):
        self.stop_flag = True


class Worker_acc(QThread):
    message = pyqtSignal(str)
    finished = pyqtSignal()
    
    con = sqlite3.connect("db.db")
    ips = con.execute("SELECT value FROM params WHERE params.name = 'ip_acc'")
    ip = ips.fetchone()
    ports = con.execute("SELECT value FROM params WHERE params.name = 'port_acc'")
    port = ports.fetchone()
    urls = con.execute("SELECT value FROM params WHERE params.name = 'url'")
    url = urls.fetchone()
    con.close()

    def __init__(self):
        super().__init__()
        self.stop_flag = False

    def run(self):
        print("running thread acc")
        self.finished.emit()
        conn = None
        zk = ZK((self.ip[0]), port=(int(self.port[0])), verbose=True)
        conn = zk.connect()
        
        while True:
            for attendance in conn.live_capture():
                if attendance is None:
                    pass
                else:
                    response = requests.get(self.url[0] + "/api/accesslog/" + attendance.user_id)
                    if response.status_code == 200:
                        user = response.json()
                        self.message.emit(str(user["name"]))
                        time.sleep(5)
                        self.finished.emit()
                    else:
                        message = response.json()
                        self.message.emit(str(message["message"]))
                        time.sleep(5)
                        self.finished.emit()

    def stop(self):
        self.stop_flag = True


class Worker_sor(QThread):
    message = pyqtSignal(str)
    finished = pyqtSignal()
    
    con = sqlite3.connect("db.db")
    ips = con.execute("SELECT value FROM params WHERE params.name = 'ip_sor'")
    ip = ips.fetchone()
    ports = con.execute("SELECT value FROM params WHERE params.name = 'port_sor'")
    port = ports.fetchone()
    urls = con.execute("SELECT value FROM params WHERE params.name = 'url'")
    url = urls.fetchone()
    con.close()

    def __init__(self):
        super().__init__()
        self.stop_flag = False

    def run(self):
        print("running thread acc")
        self.finished.emit()
        conn = None
        zk = ZK((self.ip[0]), port=(int(self.port[0])), verbose=True)
        conn = zk.connect()
        
        while True:
            for attendance in conn.live_capture():
                if attendance is None:
                    pass
                else:
                    response = requests.get(self.url[0] + "/api/sortlog/" + attendance.user_id)
                    if response.status_code == 200:
                        user = response.json()
                        self.message.emit(str(user["name"]))
                        time.sleep(5)
                        self.finished.emit()
                    else:
                        message = response.json()
                        self.message.emit(str(message["message"]))
                        time.sleep(5)
                        self.finished.emit()

    def stop(self):
        self.stop_flag = True


class ConnectWindow(QMainWindow):

    def __init__(self, parent=None):
        super().__init__(parent)
        loadUi("widget.ui", self)
        self.parent().setEnabled(False)
        logo = QPixmap("logo.png")
        self.setWindowIcon(QIcon(logo))
        self.setEnabled(True)
        self.setWindowTitle("Params")

        con = sqlite3.connect("db.db")
        ips = con.execute("SELECT value FROM params WHERE params.name = 'ip'")
        row = ips.fetchone()
        ports = con.execute("SELECT value FROM params WHERE params.name = 'port'")
        roww = ports.fetchone()
        url = con.execute("SELECT value FROM params WHERE params.name = 'url'")
        rowww = url.fetchone()
        ip_acc = con.execute("SELECT value FROM params WHERE params.name = 'ip_acc'")
        ip_acc = ip_acc.fetchone()
        port_acc = con.execute("SELECT value FROM params WHERE params.name = 'port_acc'")
        port_acc = port_acc.fetchone()
        ip_sor = con.execute("SELECT value FROM params WHERE params.name = 'ip_sor'")
        ip_sor = ip_sor.fetchone()
        port_sor = con.execute("SELECT value FROM params WHERE params.name = 'port_sor'")
        port_sor = port_sor.fetchone()
        etab_name = con.execute("SELECT value FROM params WHERE params.name = 'etab_name'")
        etab_name = etab_name.fetchone()
        dou_name = con.execute("SELECT value FROM params WHERE params.name = 'dou_name'")
        dou_name = dou_name.fetchone()

        self.ip.setText(row[0])
        self.port.setText(roww[0])
        self.ip_acctx.setText(ip_acc[0])
        self.port_acctx.setText(port_acc[0])
        self.ip_sortx.setText(ip_sor[0])
        self.port_sortx.setText(port_sor[0])
        self.etabname.setText(etab_name[0])
        self.douname.setText(dou_name[0])
        self.serverurl.setText(rowww[0])
        con.close()

        self.closebtn.clicked.connect(self.close)
        self.connectbtn.clicked.connect(self.connect_res)
        self.connectbtn_2.clicked.connect(self.connect_acc)
        self.connectbtn_3.clicked.connect(self.connect_sor)
        self.connectbtn_4.clicked.connect(self.connect_server)
        self.etab_set.clicked.connect(self.set_etab_name)
        self.dou_set.clicked.connect(self.set_dou_name)
        self.sync.clicked.connect(self.sync_users_con)
        self.sync_acc.clicked.connect(self.sync_users_acc)
        self.sync_sor.clicked.connect(self.sync_users_sor)

    def connect_res(self):
        try:
            print(self.ip.text())
            print(self.port.text())
            zk = ZK((self.ip.text()), port=(int(self.port.text())), verbose=True)
            conn = zk.connect()
            if conn:
                conn.disconnect()
                conn = sqlite3.connect("db.db")
                c = conn.cursor()
                c.execute("UPDATE params SET value = ? WHERE params.name = ? ", (self.ip.text(), "ip"))
                c.execute("UPDATE params SET value = ? WHERE params.name = ? ", (self.port.text(), "port"))
                conn.commit()
                conn.close()
                self.errors.setText("تم الاتصال بقاعدة البيانات بنجاح")
        except Exception as e:
            try:
                self.errors.setText(str(e))
                print(e)
            finally:
                e = None
                del e

    def connect_acc(self):
        try:
            print(self.ip_acctx.text())
            print(self.port_acctx.text())
            zk = ZK((self.ip_acctx.text()), port=(int(self.port_acctx.text())), verbose=True)
            conn = zk.connect()
            if conn:
                conn.disconnect()
                conn = sqlite3.connect("db.db")
                c = conn.cursor()
                c.execute("UPDATE params SET value = ? WHERE params.name = ? ", (self.ip_acctx.text(), "ip_acc"))
                c.execute("UPDATE params SET value = ? WHERE params.name = ? ", (self.port_acctx.text(), "port_acc"))
                conn.commit()
                conn.close()
                self.errors.setText("تم الاتصال بقاعدة الدخول بنجاح")
        except Exception as e:
            try:
                self.errors.setText(str(e))
                print(e)
            finally:
                e = None
                del e

    def connect_sor(self):
        try:
            print(self.ip_sortx.text())
            print(self.port_sortx.text())
            zk = ZK((self.ip_sortx.text()), port=(int(self.port_sortx.text())), verbose=True)
            conn = zk.connect()
            if conn:
                conn.disconnect()
                conn = sqlite3.connect("db.db")
                c = conn.cursor()
                c.execute("UPDATE params SET value = ? WHERE params.name = ? ", (self.ip_sortx.text(), "ip_sor"))
                c.execute("UPDATE params SET value = ? WHERE params.name = ? ", (self.port_sortx.text(), "port_sor"))
                conn.commit()
                conn.close()
                self.errors.setText("تم الاتصال بقاعدة الخروج بنجاح")
        except Exception as e:
            try:
                self.errors.setText(str(e))
                print(e)
            finally:
                e = None
                del e

    def connect_server(self):
        try:
            print(self.serverurl.text())
            response = requests.get(self.serverurl.text())
            if response.status_code == 200:
                conn = sqlite3.connect("db.db")
                c = conn.cursor()
                c.execute("UPDATE params SET value = ? WHERE params.name = ? ", (self.serverurl.text(), "url"))
                conn.commit()
                conn.close()
                self.errors.setText("تم الاتصال بالخادم المركزي بنجاح")
        except Exception as e:
            try:
                self.errors.setText(str(e))
                print(e)
            finally:
                e = None
                del e

    def set_etab_name(self):
        conn = sqlite3.connect("db.db")
        c = conn.cursor()
        c.execute("UPDATE params SET value = ? WHERE params.name = ? ", (self.etabname.text(), "etab_name"))
        conn.commit()
        conn.close()
        self.errors.setText("تم الاتصال باسم المؤسسة")
        self.parent().etabmsg.setText(self.etabname.text())

    def set_dou_name(self):
        conn = sqlite3.connect("db.db")
        c = conn.cursor()
        c.execute("UPDATE params SET value = ? WHERE params.name = ? ", (self.douname.text(), "dou_name"))
        conn.commit()
        conn.close()
        self.errors.setText("تم الاتصال باسم المديرية")
        self.parent().doumsg.setText(self.douname.text())

    def closeEvent(self, event):
        self.parent().setEnabled(True)
        self.parent().activateWindow()
        event.accept()

    def sync_users_acc(self):
        con = sqlite3.connect("db.db")
        ips = con.execute("SELECT value FROM params WHERE params.name = 'ip_acc'")
        ip = ips.fetchone()
        ports = con.execute("SELECT value FROM params WHERE params.name = 'port_acc'")
        port = ports.fetchone()
        urls = con.execute("SELECT value FROM params WHERE params.name = 'url'")
        url = urls.fetchone()
        last_update_acc = con.execute("SELECT value FROM params WHERE params.name = 'last_update_acc'")
        last_update_acc = last_update_acc.fetchone()
        con.close()

        zk = ZK((ip[0]), port=(int(port[0])), verbose=True)
        conn = zk.connect()
        urll = url[0] + "/api/users/"
        data = {"last_update": (last_update_acc[0])}
        response = requests.get(urll, params=data)

        if response.status_code == 200:
            users = response.json()
            for user in users:
                print(user["user_id"])
                conn.set_user(uid=(user["id"]), name=(user["name"]), privilege=(const.USER_DEFAULT),
                             password="123456", user_id=(user["user_id"]), card=(int(user["card_id"] or 0)))
            else:
                conn.test_voice(0)
                self.errors.setText("تم تحديث قاعدة البيانات بنجاح")
                conn = sqlite3.connect("db.db")
                c = conn.cursor()
                timestamp = datetime.datetime.now()
                formatted_timestamp = timestamp.strftime("%Y-%m-%d %H:%M:%S")
                print(formatted_timestamp)
                c.execute("UPDATE params SET value = ? WHERE params.name = ? ", (formatted_timestamp, "last_update_acc"))
                conn.commit()
                conn.close()
        else:
            self.errors.setText("خطأ")

    def sync_users_sor(self):
        con = sqlite3.connect("db.db")
        ips = con.execute("SELECT value FROM params WHERE params.name = 'ip_sor'")
        ip = ips.fetchone()
        ports = con.execute("SELECT value FROM params WHERE params.name = 'port_sor'")
        port = ports.fetchone()
        urls = con.execute("SELECT value FROM params WHERE params.name = 'url'")
        url = urls.fetchone()
        last_update_sor = con.execute("SELECT value FROM params WHERE params.name = 'last_update_sor'")
        last_update_sor = last_update_sor.fetchone()
        con.close()

        zk = ZK((ip[0]), port=(int(port[0])), verbose=True)
        conn = zk.connect()
        urll = url[0] + "/api/users/"
        data = {"last_update": (last_update_sor[0])}
        response = requests.get(urll, params=data)

        if response.status_code == 200:
            users = response.json()
            for user in users:
                print(user["user_id"])
                conn.set_user(uid=(user["id"]), name=(user["name"]), privilege=(const.USER_DEFAULT),
                             password="123456", user_id=(user["user_id"]), card=(int(user["card_id"] or 0)))
            else:
                conn.test_voice(0)
                self.errors.setText("تم تحديث قاعدة البيانات للخروج بنجاح")
                conn = sqlite3.connect("db.db")
                c = conn.cursor()
                timestamp = datetime.datetime.now()
                formatted_timestamp = timestamp.strftime("%Y-%m-%d %H:%M:%S")
                print(formatted_timestamp)
                c.execute("UPDATE params SET value = ? WHERE params.name = ? ", (formatted_timestamp, "last_update_sor"))
                conn.commit()
                conn.close()
        else:
            self.errors.setText("خطأ  ")

    def sync_users_con(self):
        con = sqlite3.connect("db.db")
        ips = con.execute("SELECT value FROM params WHERE params.name = 'ip'")
        ip = ips.fetchone()
        ports = con.execute("SELECT value FROM params WHERE params.name = 'port'")
        port = ports.fetchone()
        urls = con.execute("SELECT value FROM params WHERE params.name = 'url'")
        url = urls.fetchone()
        last_update_con = con.execute("SELECT value FROM params WHERE params.name = 'last_update_con'")
        last_update_con = last_update_con.fetchone()
        con.close()

        zk = ZK((ip[0]), port=(int(port[0])), verbose=True)
        conn = zk.connect()
        urll = url[0] + "/api/users/"
        data = {"last_update": (last_update_con[0])}
        response = requests.get(urll, params=data)
        print(urll)
        print(data)
        print(response)

        if response.status_code == 200:
            users = response.json()
            for user in users:
                print(user["user_id"])
                conn.set_user(uid=(user["id"]), name=(user["name"]), privilege=(const.USER_DEFAULT),
                             password="123456", user_id=(user["user_id"]), card=(int(user["card_id"] or 0)))
            else:
                conn.test_voice(0)
                self.errors.setText("تم تحديث قاعدة البيانات للطعام بنجاح")
                conn = sqlite3.connect("db.db")
                c = conn.cursor()
                timestamp = datetime.datetime.now()
                formatted_timestamp = timestamp.strftime("%Y-%m-%d %H:%M:%S")
                print(formatted_timestamp)
                c.execute("UPDATE params SET value = ? WHERE params.name = ? ", (formatted_timestamp, "last_update_con"))
                conn.commit()
                conn.close()
        else:
            self.errors.setText("خطأ")


class MainWindow(QMainWindow):
    thread = Worker()
    threadsor = Worker_sor()
    threadacc = Worker_acc()

    def __init__(self):
        super().__init__()
        loadUi("mm.ui", self)
        logo = QPixmap("logo.png")
        self.setWindowIcon(QIcon(logo))
        self.image_logo.setPixmap(logo)
        self.image_logo.setScaledContents(True)

        con = sqlite3.connect("db.db")
        etab_name = con.execute("SELECT value FROM params WHERE params.name = 'etab_name'")
        etab_name = etab_name.fetchone()
        dou_name = con.execute("SELECT value FROM params WHERE params.name = 'dou_name'")
        dou_name = dou_name.fetchone()
        self.etabmsg.setText(etab_name[0])
        self.doumsg.setText(dou_name[0])
        con.close

        printers = win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL, None, 1)
        for printer in printers:
            self.comboBox.addItem(str(printer[2]))
        else:
            self.startbutton.clicked.connect(self.start)
            self.startbutton_acc.clicked.connect(self.startacc)
            self.startbutton_sor.clicked.connect(self.startsor)
            self.startbutton_2.clicked.connect(self.exit_app)
            self.connectbtn.clicked.connect(self.show_second_window)
            self.comboBox.currentIndexChanged.connect(self.handleComboSelection)

    def startsor(self):
        print("started... sor")
        self.startbutton_sor.setEnabled(False)
        self.startbutton_sor.setText("الخروج قيد التشغيل")
        self.threadsor.finished.connect(self.finishsor)
        self.threadsor.message.connect(self.sort_lab.setText)
        self.threadsor.start()

    def startacc(self):
        print("started... acc")
        self.startbutton_acc.setEnabled(False)
        self.startbutton_acc.setText("الدخول قيد التشغيل")
        self.threadacc.finished.connect(self.finishacc)
        self.threadacc.message.connect(self.enter_lab.setText)
        self.threadacc.start()

    def start(self):
        print("started...")
        self.startbutton.setEnabled(False)
        self.startbutton.setText("الطعام قيد التشغيل")
        self.threadacc.finished.connect(self.finishacc)
        self.threadacc.message.connect(self.enter_lab.setText)
        self.threadsor.finished.connect(self.finishsor)
        self.threadsor.message.connect(self.sort_lab.setText)
        self.thread.finished.connect(self.finish)
        self.thread.message_color.connect(self.message.setStyleSheet)
        self.thread.message.connect(self.message.setText)
        self.thread.meal_name.connect(self.meal_name.setText)
        self.thread.meal_num.connect(self.meal_num.setText)
        self.thread.button.connect(self.name.setText)
        self.thread.button2.connect(self.name_2.setText)
        self.thread.image.connect(self.set_image)
        self.thread.start()

    def show_second_window(self):
        self.second_window = ConnectWindow(parent=self)
        self.second_window.show()

    def finishacc(self):
        print("hi this is finish")
        self.enter_lab.setText("")

    def finishsor(self):
        print("hi this is finish")
        self.sort_lab.setText("")

    def finish(self):
        print("hi this is finish")
        self.set_imageo()
        self.name.setText("")
        self.name_2.setText("")
        self.message.setText("")

    def exit_app(self):
        QApplication.quit()

    def set_image(self, url):
        data = urllib.request.urlopen(url).read()
        pixmap = QPixmap()
        pixmap.loadFromData(data)
        pixmap.scaledToWidth(400)
        pixmap.scaledToHeight(400)
        self.label.setPixmap(pixmap)
        self.label.setScaledContents(True)

    def set_imageo(self):
        image_path = "profile.jpg"
        pixmap = QPixmap(image_path)
        self.label.setPixmap(pixmap)
        self.label.setScaledContents(True)

    def handleComboSelection(self, index):
        selectedOption = self.comboBox.currentText()
        conn = sqlite3.connect("db.db")
        c = conn.cursor()
        c.execute("UPDATE params SET value = ? WHERE params.name = ? ", (selectedOption, "printer"))
        conn.commit()
        conn.close()
        print(f"Selected option: {selectedOption}")


if __name__ == "__main__":
    app = QApplication([])
    window = MainWindow()
    window.setWindowTitle("Aissa")
    window.show()
    app.exec_()
