# نظام إدارة الإطعام في الإقامة الجامعية - ONOU

## وصف المشروع

هذا المشروع عبارة عن نظام إدارة الإطعام في الإقامة الجامعية مطور باستخدام Python و PyQt5. يتيح النظام إدارة عمليات الإطعام للطلاب من خلال أجهزة البصمة والبطاقات الذكية.

## المميزات الرئيسية

### 1. إدارة الحضور والإطعام
- **تتبع الحضور**: مراقبة دخول وخروج الطلاب عبر أجهزة البصمة
- **إدارة الوجبات**: تسجيل وتتبع الوجبات المقدمة للطلاب
- **طباعة الإيصالات**: طباعة إيصالات الوجبات على طابعة POS-80

### 2. الاتصال بالأجهزة
- **أجهزة البصمة ZK**: الاتصال بأجهزة البصمة لقراءة بيانات الحضور
- **دعم متعدد الأجهزة**: إمكانية الاتصال بعدة أجهزة (طعام، دخول، خروج)
- **مزامنة البيانات**: مزامنة بيانات المستخدمين مع الخادم المركزي

### 3. واجهة المستخدم
- **واجهة عربية**: دعم كامل للغة العربية
- **سهولة الاستخدام**: واجهة بسيطة وسهلة الاستخدام
- **عرض الصور**: عرض صور الطلاب عند التسجيل

## التقنيات المستخدمة

- **Python 3.8**: لغة البرمجة الأساسية
- **PyQt5**: إطار العمل لبناء واجهة المستخدم الرسومية
- **SQLite**: قاعدة البيانات المحلية
- **pyzk**: مكتبة للاتصال بأجهزة البصمة ZK
- **requests**: للاتصال بالخادم المركزي
- **win32print**: لطباعة الإيصالات

## هيكل المشروع

```
ONOU/
├── mm1_clean.py          # الكود المصدري الرئيسي
├── mm.ui                 # واجهة المستخدم الرئيسية
├── widget.ui             # واجهة الإعدادات
├── db.db                 # قاعدة البيانات المحلية
├── logo.png              # شعار التطبيق
├── profile.jpg           # صورة افتراضية
├── set.png               # أيقونة الإعدادات
├── requirements.txt      # المكتبات المطلوبة
└── README.md            # ملف التوثيق
```

## الفئات الرئيسية

### 1. Worker
- **الغرض**: إدارة عمليات الإطعام الرئيسية
- **الوظائف**: 
  - الاتصال بجهاز البصمة
  - معالجة بيانات الحضور
  - طباعة الإيصالات
  - التواصل مع الخادم المركزي

### 2. Worker_acc
- **الغرض**: إدارة عمليات الدخول
- **الوظائف**: تسجيل دخول الطلاب

### 3. Worker_sor
- **الغرض**: إدارة عمليات الخروج
- **الوظائف**: تسجيل خروج الطلاب

### 4. ConnectWindow
- **الغرض**: نافذة الإعدادات والتكوين
- **الوظائف**:
  - تكوين عناوين IP والمنافذ
  - إعدادات الطابعة
  - مزامنة بيانات المستخدمين
  - إعدادات المؤسسة

### 5. MainWindow
- **الغرض**: النافذة الرئيسية للتطبيق
- **الوظائف**:
  - عرض واجهة المستخدم الرئيسية
  - إدارة العمليات المختلفة
  - عرض معلومات الطلاب والوجبات

## قاعدة البيانات

### جدول params
يحتوي على إعدادات النظام:
- **ip, port**: عناوين أجهزة البصمة
- **ip_acc, port_acc**: إعدادات جهاز الدخول
- **ip_sor, port_sor**: إعدادات جهاز الخروج
- **url**: رابط الخادم المركزي
- **printer**: اسم الطابعة المحددة
- **etab_name**: اسم المؤسسة
- **dou_name**: اسم المديرية
- **last_update_***: تواريخ آخر تحديث

## API الخادم المركزي

### نقاط النهاية (Endpoints)
- `GET /api/attendance/{user_id}`: الحصول على بيانات الإطعام
- `GET /api/accesslog/{user_id}`: تسجيل الدخول
- `GET /api/sortlog/{user_id}`: تسجيل الخروج
- `GET /api/users/`: الحصول على قائمة المستخدمين
- `GET /photos/{photo_id}`: الحصول على صور الطلاب

## التثبيت والتشغيل

### المتطلبات
- Python 3.8 أو أحدث
- Windows (للطباعة)
- أجهزة البصمة ZK متوافقة

### خطوات التثبيت
1. تثبيت المكتبات المطلوبة:
```bash
pip install -r requirements.txt
```

2. تشغيل التطبيق:
```bash
python mm1_clean.py
```

## الاستخدام

### 1. الإعدادات الأولية
- افتح نافذة الإعدادات من الزر المخصص
- أدخل عناوين IP ومنافذ أجهزة البصمة
- حدد رابط الخادم المركزي
- اختر الطابعة المناسبة

### 2. مزامنة البيانات
- استخدم أزرار المزامنة لتحديث بيانات المستخدمين
- تأكد من الاتصال بالخادم المركزي

### 3. بدء التشغيل
- اضغط على "تشغيل الإطعام" لبدء مراقبة عمليات الإطعام
- اضغط على أزرار الدخول والخروج حسب الحاجة

## الطباعة

يدعم النظام طباعة إيصالات تحتوي على:
- تاريخ ووقت العملية
- اسم الطالب ورقمه الجامعي
- نوع الوجبة ورقمها
- رسالة الحالة

## المؤسسة

- **اسم المؤسسة**: الإقامة الجامعية طالب عبد الرحمان
- **المديرية**: مديرية الخدمات الجامعية الجزائر غرب

## الدعم والصيانة

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

## الملفات المستخرجة

تم استخراج الكود المصدري من الملف التنفيذي `mm1.exe` وإنشاء الملفات التالية:

### الملفات الرئيسية
- **mm1_clean.py**: الكود المصدري الكامل للتطبيق
- **config.py**: ملف التكوين والإعدادات
- **setup_database.py**: إعداد قاعدة البيانات
- **test_connection.py**: اختبار الاتصالات
- **run_app.py**: تشغيل التطبيق مع معالجة الأخطاء
- **start_app.bat**: ملف batch لتشغيل التطبيق على Windows

### ملفات التوثيق
- **README.md**: دليل المشروع (هذا الملف)
- **requirements.txt**: قائمة المكتبات المطلوبة

## طرق التشغيل

### الطريقة الأولى: استخدام ملف batch (Windows)
```bash
start_app.bat
```

### الطريقة الثانية: استخدام Python مباشرة
```bash
# تثبيت المكتبات
pip install -r requirements.txt

# تشغيل التطبيق
python run_app.py
```

### الطريقة الثالثة: تشغيل الكود الأصلي
```bash
python mm1_clean.py
```

## أدوات المساعدة

### إعداد قاعدة البيانات
```bash
python setup_database.py
```

### اختبار الاتصالات
```bash
python test_connection.py
```

### فحص المتطلبات
```bash
python run_app.py --check
```

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في استيراد المكتبات
```
ModuleNotFoundError: No module named 'PyQt5'
```
**الحل**: تثبيت المكتبات المطلوبة
```bash
pip install -r requirements.txt
```

#### 2. خطأ في الاتصال بأجهزة البصمة
```
ConnectionError: Unable to connect to device
```
**الحل**:
- تحقق من عناوين IP والمنافذ
- تأكد من أن الأجهزة متصلة بالشبكة
- استخدم أداة اختبار الاتصال

#### 3. خطأ في قاعدة البيانات
```
sqlite3.OperationalError: no such table: params
```
**الحل**: إعادة إنشاء قاعدة البيانات
```bash
python setup_database.py
```

#### 4. مشاكل الطباعة
```
win32print.error: The printer name is invalid
```
**الحل**:
- تحقق من تثبيت الطابعة
- تحديث اسم الطابعة في الإعدادات

### ملفات السجلات
يتم حفظ سجلات التطبيق في مجلد `logs/` مع اسم الملف:
```
logs/app_YYYYMMDD.log
```

## التطوير والتخصيص

### إضافة ميزات جديدة
1. قم بتعديل الكود في `mm1_clean.py`
2. حدث ملفات واجهة المستخدم (.ui) حسب الحاجة
3. أضف المعاملات الجديدة في `config.py`
4. حدث قاعدة البيانات في `setup_database.py`

### إنشاء ملف تنفيذي جديد
```bash
pip install pyinstaller
pyinstaller --onefile --windowed mm1_clean.py
```

## الترخيص

هذا المشروع مطور للاستخدام الداخلي في الإقامة الجامعية.

## معلومات الاستخراج

- **تاريخ الاستخراج**: 2024
- **الأداة المستخدمة**: pyinstxtractor-ng + uncompyle6
- **حالة الاستخراج**: مكتمل بنجاح
- **عدد الأسطر**: 599 سطر
- **اللغة**: Python 3.8
