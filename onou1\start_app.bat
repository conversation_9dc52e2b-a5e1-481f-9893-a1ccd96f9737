@echo off
chcp 65001 > nul
title نظام إدارة الإطعام - ONOU

echo ================================================
echo نظام إدارة الإطعام في الإقامة الجامعية
echo ONOU - University Residence Meal Management
echo ================================================
echo.

REM التحقق من وجود Python
python --version > nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.8 أو أحدث
    pause
    exit /b 1
)

REM التحقق من وجود pip
pip --version > nul 2>&1
if errorlevel 1 (
    echo خطأ: pip غير متوفر
    echo يرجى التأكد من تثبيت Python بشكل صحيح
    pause
    exit /b 1
)

REM التحقق من وجود ملف المتطلبات
if not exist requirements.txt (
    echo خطأ: ملف requirements.txt غير موجود
    pause
    exit /b 1
)

echo تثبيت المكتبات المطلوبة...
pip install -r requirements.txt

if errorlevel 1 (
    echo خطأ في تثبيت المكتبات المطلوبة
    pause
    exit /b 1
)

echo.
echo تشغيل التطبيق...
python run_app.py

if errorlevel 1 (
    echo خطأ في تشغيل التطبيق
    pause
    exit /b 1
)

pause
