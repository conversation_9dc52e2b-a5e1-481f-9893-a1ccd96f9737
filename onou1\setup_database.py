#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد قاعدة البيانات للمشروع
Database setup for ONOU project
"""

import sqlite3
import datetime
from config import DEFAULT_NETWORK_CONFIG, INSTITUTION_CONFIG, PRINTER_CONFIG

def create_database():
    """إنشاء قاعدة البيانات والجداول"""
    try:
        conn = sqlite3.connect("db.db")
        cursor = conn.cursor()
        
        # إنشاء جدول المعاملات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS params (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                value TEXT,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إنشاء جدول المستخدمين (اختياري للنسخ الاحتياطي)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users_backup (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                card_id TEXT,
                photo_id TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إنشاء جدول السجلات (اختياري للنسخ الاحتياطي)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS attendance_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                attendance_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                device_type TEXT,
                status TEXT,
                meal_type TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        print("✓ تم إنشاء قاعدة البيانات والجداول بنجاح")
        return conn
        
    except Exception as e:
        print(f"✗ خطأ في إنشاء قاعدة البيانات: {e}")
        return None

def insert_default_params(conn):
    """إدراج المعاملات الافتراضية"""
    cursor = conn.cursor()
    
    # المعاملات الافتراضية
    default_params = [
        # إعدادات الشبكة
        ('ip', DEFAULT_NETWORK_CONFIG['ip'], 'عنوان IP لجهاز الطعام'),
        ('port', str(DEFAULT_NETWORK_CONFIG['port']), 'منفذ جهاز الطعام'),
        ('ip_acc', DEFAULT_NETWORK_CONFIG['ip_acc'], 'عنوان IP لجهاز الدخول'),
        ('port_acc', str(DEFAULT_NETWORK_CONFIG['port_acc']), 'منفذ جهاز الدخول'),
        ('ip_sor', DEFAULT_NETWORK_CONFIG['ip_sor'], 'عنوان IP لجهاز الخروج'),
        ('port_sor', str(DEFAULT_NETWORK_CONFIG['port_sor']), 'منفذ جهاز الخروج'),
        ('url', DEFAULT_NETWORK_CONFIG['url'], 'رابط الخادم المركزي'),
        
        # إعدادات المؤسسة
        ('etab_name', INSTITUTION_CONFIG['etab_name'], 'اسم المؤسسة'),
        ('dou_name', INSTITUTION_CONFIG['dou_name'], 'اسم المديرية'),
        
        # إعدادات الطباعة
        ('printer', PRINTER_CONFIG['default_printer'], 'اسم الطابعة المحددة'),
        
        # تواريخ آخر تحديث
        ('last_update_con', '2024-01-01 00:00:00', 'آخر تحديث لبيانات الطعام'),
        ('last_update_acc', '2024-01-01 00:00:00', 'آخر تحديث لبيانات الدخول'),
        ('last_update_sor', '2024-01-01 00:00:00', 'آخر تحديث لبيانات الخروج'),
        
        # إعدادات التطبيق
        ('app_version', '1.0.0', 'إصدار التطبيق'),
        ('language', 'ar', 'لغة التطبيق'),
        ('theme', 'default', 'مظهر التطبيق'),
        
        # إعدادات الأمان
        ('max_retry', '3', 'عدد المحاولات القصوى'),
        ('session_timeout', '3600', 'مهلة انتهاء الجلسة بالثواني'),
        
        # إعدادات النظام
        ('auto_sync', '1', 'المزامنة التلقائية (1=مفعل, 0=معطل)'),
        ('debug_mode', '0', 'وضع التطوير (1=مفعل, 0=معطل)'),
        ('log_level', 'INFO', 'مستوى السجلات'),
    ]
    
    try:
        for name, value, description in default_params:
            cursor.execute('''
                INSERT OR IGNORE INTO params (name, value, description)
                VALUES (?, ?, ?)
            ''', (name, value, description))
        
        conn.commit()
        print("✓ تم إدراج المعاملات الافتراضية بنجاح")
        
    except Exception as e:
        print(f"✗ خطأ في إدراج المعاملات: {e}")

def update_param(conn, name, value):
    """تحديث معامل محدد"""
    try:
        cursor = conn.cursor()
        cursor.execute('''
            UPDATE params 
            SET value = ?, updated_at = CURRENT_TIMESTAMP 
            WHERE name = ?
        ''', (value, name))
        
        if cursor.rowcount > 0:
            conn.commit()
            print(f"✓ تم تحديث {name} = {value}")
        else:
            print(f"✗ لم يتم العثور على المعامل: {name}")
            
    except Exception as e:
        print(f"✗ خطأ في تحديث المعامل {name}: {e}")

def get_param(conn, name):
    """الحصول على قيمة معامل"""
    try:
        cursor = conn.cursor()
        cursor.execute('SELECT value FROM params WHERE name = ?', (name,))
        result = cursor.fetchone()
        
        if result:
            return result[0]
        else:
            return None
            
    except Exception as e:
        print(f"✗ خطأ في قراءة المعامل {name}: {e}")
        return None

def list_all_params(conn):
    """عرض جميع المعاملات"""
    try:
        cursor = conn.cursor()
        cursor.execute('''
            SELECT name, value, description, updated_at 
            FROM params 
            ORDER BY name
        ''')
        
        params = cursor.fetchall()
        
        print("\nجميع المعاملات المحفوظة:")
        print("-" * 80)
        print(f"{'الاسم':<20} {'القيمة':<30} {'الوصف':<30}")
        print("-" * 80)
        
        for name, value, description, updated_at in params:
            print(f"{name:<20} {value:<30} {description or '':<30}")
        
        print("-" * 80)
        print(f"إجمالي المعاملات: {len(params)}")
        
    except Exception as e:
        print(f"✗ خطأ في عرض المعاملات: {e}")

def backup_database(conn, backup_file="db_backup.sql"):
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    try:
        with open(backup_file, 'w', encoding='utf-8') as f:
            for line in conn.iterdump():
                f.write('%s\n' % line)
        
        print(f"✓ تم إنشاء نسخة احتياطية: {backup_file}")
        
    except Exception as e:
        print(f"✗ خطأ في إنشاء النسخة الاحتياطية: {e}")

def main():
    """الدالة الرئيسية لإعداد قاعدة البيانات"""
    print("=" * 50)
    print("إعداد قاعدة بيانات نظام إدارة الإطعام")
    print("=" * 50)
    
    # إنشاء قاعدة البيانات
    conn = create_database()
    if not conn:
        return
    
    try:
        # إدراج المعاملات الافتراضية
        insert_default_params(conn)
        
        # عرض جميع المعاملات
        list_all_params(conn)
        
        # إنشاء نسخة احتياطية
        backup_database(conn)
        
        print("\n✓ تم إعداد قاعدة البيانات بنجاح!")
        print("يمكنك الآن تشغيل التطبيق الرئيسي.")
        
    finally:
        conn.close()

if __name__ == "__main__":
    main()
