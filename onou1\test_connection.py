#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الاتصال بأجهزة البصمة والخادم المركزي
Test connection to fingerprint devices and central server
"""

import sqlite3
import requests
from zk import ZK
import sys
import time

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        conn = sqlite3.connect("db.db")
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        conn.close()
        print("✓ تم الاتصال بقاعدة البيانات بنجاح")
        print(f"الجداول الموجودة: {[table[0] for table in tables]}")
        return True
    except Exception as e:
        print(f"✗ خطأ في الاتصال بقاعدة البيانات: {e}")
        return False

def get_config_from_db():
    """الحصول على الإعدادات من قاعدة البيانات"""
    try:
        conn = sqlite3.connect("db.db")
        cursor = conn.cursor()
        
        # الحصول على جميع الإعدادات
        cursor.execute("SELECT name, value FROM params")
        params = dict(cursor.fetchall())
        conn.close()
        
        return params
    except Exception as e:
        print(f"خطأ في قراءة الإعدادات: {e}")
        return {}

def test_fingerprint_device(ip, port, device_name):
    """اختبار الاتصال بجهاز البصمة"""
    try:
        print(f"\nاختبار الاتصال بجهاز {device_name}...")
        print(f"IP: {ip}, Port: {port}")
        
        zk = ZK(ip, port=int(port), timeout=5, verbose=False)
        conn = zk.connect()
        
        if conn:
            # الحصول على معلومات الجهاز
            firmware_version = conn.get_firmware_version()
            device_name_info = conn.get_device_name()
            users_count = len(conn.get_users())
            
            print(f"✓ تم الاتصال بجهاز {device_name} بنجاح")
            print(f"  - إصدار البرنامج الثابت: {firmware_version}")
            print(f"  - اسم الجهاز: {device_name_info}")
            print(f"  - عدد المستخدمين: {users_count}")
            
            conn.disconnect()
            return True
        else:
            print(f"✗ فشل الاتصال بجهاز {device_name}")
            return False
            
    except Exception as e:
        print(f"✗ خطأ في الاتصال بجهاز {device_name}: {e}")
        return False

def test_server_connection(url):
    """اختبار الاتصال بالخادم المركزي"""
    try:
        print(f"\nاختبار الاتصال بالخادم المركزي...")
        print(f"URL: {url}")
        
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            print("✓ تم الاتصال بالخادم المركزي بنجاح")
            print(f"  - كود الاستجابة: {response.status_code}")
            return True
        else:
            print(f"✗ خطأ في الاتصال بالخادم: كود الاستجابة {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print("✗ انتهت مهلة الاتصال بالخادم")
        return False
    except requests.exceptions.ConnectionError:
        print("✗ خطأ في الاتصال بالخادم")
        return False
    except Exception as e:
        print(f"✗ خطأ في الاتصال بالخادم: {e}")
        return False

def test_api_endpoints(base_url):
    """اختبار نقاط النهاية للAPI"""
    endpoints = [
        '/api/users/',
        '/api/attendance/test',
        '/api/accesslog/test',
        '/api/sortlog/test'
    ]
    
    print(f"\nاختبار نقاط النهاية للAPI...")
    
    for endpoint in endpoints:
        try:
            url = base_url + endpoint
            response = requests.get(url, timeout=5)
            
            if response.status_code in [200, 404]:  # 404 مقبول للاختبار
                print(f"✓ {endpoint}: متاح")
            else:
                print(f"✗ {endpoint}: غير متاح (كود: {response.status_code})")
                
        except Exception as e:
            print(f"✗ {endpoint}: خطأ - {e}")

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 50)
    print("اختبار اتصالات نظام إدارة الإطعام")
    print("=" * 50)
    
    # اختبار قاعدة البيانات
    if not test_database_connection():
        print("توقف الاختبار بسبب خطأ في قاعدة البيانات")
        return
    
    # الحصول على الإعدادات
    config = get_config_from_db()
    
    if not config:
        print("لا يمكن قراءة الإعدادات من قاعدة البيانات")
        return
    
    # اختبار أجهزة البصمة
    devices = [
        (config.get('ip'), config.get('port'), 'جهاز الطعام'),
        (config.get('ip_acc'), config.get('port_acc'), 'جهاز الدخول'),
        (config.get('ip_sor'), config.get('port_sor'), 'جهاز الخروج')
    ]
    
    device_results = []
    for ip, port, name in devices:
        if ip and port:
            result = test_fingerprint_device(ip, port, name)
            device_results.append(result)
        else:
            print(f"✗ إعدادات {name} غير مكتملة")
            device_results.append(False)
    
    # اختبار الخادم المركزي
    server_url = config.get('url')
    server_result = False
    if server_url:
        server_result = test_server_connection(server_url)
        if server_result:
            test_api_endpoints(server_url)
    else:
        print("✗ رابط الخادم المركزي غير محدد")
    
    # ملخص النتائج
    print("\n" + "=" * 50)
    print("ملخص نتائج الاختبار")
    print("=" * 50)
    
    print(f"قاعدة البيانات: {'✓ متصلة' if True else '✗ غير متصلة'}")
    
    device_names = ['جهاز الطعام', 'جهاز الدخول', 'جهاز الخروج']
    for i, (result, name) in enumerate(zip(device_results, device_names)):
        status = '✓ متصل' if result else '✗ غير متصل'
        print(f"{name}: {status}")
    
    print(f"الخادم المركزي: {'✓ متصل' if server_result else '✗ غير متصل'}")
    
    # التوصيات
    print("\nالتوصيات:")
    if not any(device_results):
        print("- تحقق من عناوين IP ومنافذ أجهزة البصمة")
        print("- تأكد من أن الأجهزة متصلة بالشبكة")
    
    if not server_result:
        print("- تحقق من رابط الخادم المركزي")
        print("- تأكد من أن الخادم يعمل ومتاح")

if __name__ == "__main__":
    main()
