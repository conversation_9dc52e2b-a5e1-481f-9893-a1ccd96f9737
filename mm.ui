<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>aissa</class>
 <widget class="QMainWindow" name="aissa">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1240</width>
    <height>720</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1240</width>
    <height>720</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>1240</width>
    <height>720</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <property name="autoFillBackground">
   <bool>false</bool>
  </property>
  <property name="styleSheet">
   <string notr="true">background-color: rgb(170, 255, 255);</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <widget class="QPushButton" name="startbutton">
    <property name="enabled">
     <bool>true</bool>
    </property>
    <property name="geometry">
     <rect>
      <x>980</x>
      <y>651</y>
      <width>141</width>
      <height>31</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>7</pointsize>
      <weight>75</weight>
      <bold>true</bold>
     </font>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(85, 255, 127);</string>
    </property>
    <property name="text">
     <string>تشغيل الاطعام</string>
    </property>
    <property name="autoDefault">
     <bool>false</bool>
    </property>
   </widget>
   <widget class="QPushButton" name="startbutton_2">
    <property name="enabled">
     <bool>true</bool>
    </property>
    <property name="geometry">
     <rect>
      <x>1130</x>
      <y>651</y>
      <width>101</width>
      <height>31</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>8</pointsize>
      <weight>75</weight>
      <bold>true</bold>
     </font>
    </property>
    <property name="layoutDirection">
     <enum>Qt::LeftToRight</enum>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(255, 0, 0);</string>
    </property>
    <property name="text">
     <string>الخروج </string>
    </property>
    <property name="checkable">
     <bool>false</bool>
    </property>
    <property name="autoDefault">
     <bool>false</bool>
    </property>
   </widget>
   <widget class="QLabel" name="name">
    <property name="geometry">
     <rect>
      <x>30</x>
      <y>434</y>
      <width>461</width>
      <height>60</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>20</pointsize>
      <weight>75</weight>
      <bold>true</bold>
      <underline>false</underline>
     </font>
    </property>
    <property name="layoutDirection">
     <enum>Qt::LeftToRight</enum>
    </property>
    <property name="frameShape">
     <enum>QFrame::Panel</enum>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="textFormat">
     <enum>Qt::PlainText</enum>
    </property>
    <property name="scaledContents">
     <bool>false</bool>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
    <property name="wordWrap">
     <bool>false</bool>
    </property>
   </widget>
   <widget class="QLabel" name="name_2">
    <property name="geometry">
     <rect>
      <x>30</x>
      <y>520</y>
      <width>461</width>
      <height>60</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>20</pointsize>
      <weight>75</weight>
      <bold>true</bold>
      <underline>false</underline>
     </font>
    </property>
    <property name="layoutDirection">
     <enum>Qt::LeftToRight</enum>
    </property>
    <property name="frameShape">
     <enum>QFrame::Panel</enum>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="textFormat">
     <enum>Qt::PlainText</enum>
    </property>
    <property name="scaledContents">
     <bool>false</bool>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
    <property name="wordWrap">
     <bool>false</bool>
    </property>
   </widget>
   <widget class="QLabel" name="label">
    <property name="geometry">
     <rect>
      <x>64</x>
      <y>12</y>
      <width>400</width>
      <height>400</height>
     </rect>
    </property>
    <property name="minimumSize">
     <size>
      <width>400</width>
      <height>400</height>
     </size>
    </property>
    <property name="maximumSize">
     <size>
      <width>400</width>
      <height>400</height>
     </size>
    </property>
    <property name="layoutDirection">
     <enum>Qt::LeftToRight</enum>
    </property>
    <property name="frameShape">
     <enum>QFrame::Panel</enum>
    </property>
    <property name="frameShadow">
     <enum>QFrame::Plain</enum>
    </property>
    <property name="lineWidth">
     <number>3</number>
    </property>
    <property name="midLineWidth">
     <number>0</number>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="scaledContents">
     <bool>false</bool>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
    <property name="indent">
     <number>-1</number>
    </property>
   </widget>
   <widget class="QPushButton" name="connectbtn">
    <property name="geometry">
     <rect>
      <x>600</x>
      <y>651</y>
      <width>71</width>
      <height>31</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(255, 170, 0);
</string>
    </property>
    <property name="text">
     <string>خيارات</string>
    </property>
   </widget>
   <widget class="QComboBox" name="comboBox">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>651</y>
      <width>281</width>
      <height>31</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(255, 170, 0);</string>
    </property>
    <property name="currentText">
     <string/>
    </property>
   </widget>
   <widget class="QLabel" name="message">
    <property name="geometry">
     <rect>
      <x>530</x>
      <y>242</y>
      <width>691</width>
      <height>171</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>20</pointsize>
      <weight>75</weight>
      <bold>true</bold>
      <underline>false</underline>
     </font>
    </property>
    <property name="layoutDirection">
     <enum>Qt::LeftToRight</enum>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(170, 255, 255);</string>
    </property>
    <property name="frameShape">
     <enum>QFrame::Panel</enum>
    </property>
    <property name="lineWidth">
     <number>2</number>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="textFormat">
     <enum>Qt::PlainText</enum>
    </property>
    <property name="scaledContents">
     <bool>false</bool>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
    <property name="wordWrap">
     <bool>false</bool>
    </property>
   </widget>
   <widget class="QLabel" name="meal_num">
    <property name="geometry">
     <rect>
      <x>790</x>
      <y>483</y>
      <width>321</width>
      <height>41</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>20</pointsize>
      <weight>75</weight>
      <bold>true</bold>
      <underline>false</underline>
     </font>
    </property>
    <property name="layoutDirection">
     <enum>Qt::LeftToRight</enum>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(255, 255, 127);</string>
    </property>
    <property name="text">
     <string>0</string>
    </property>
    <property name="textFormat">
     <enum>Qt::PlainText</enum>
    </property>
    <property name="scaledContents">
     <bool>false</bool>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
    <property name="wordWrap">
     <bool>false</bool>
    </property>
   </widget>
   <widget class="QLabel" name="meal_name">
    <property name="geometry">
     <rect>
      <x>790</x>
      <y>433</y>
      <width>321</width>
      <height>41</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>20</pointsize>
      <weight>75</weight>
      <bold>true</bold>
      <underline>false</underline>
     </font>
    </property>
    <property name="layoutDirection">
     <enum>Qt::LeftToRight</enum>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(255, 255, 127);</string>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="textFormat">
     <enum>Qt::PlainText</enum>
    </property>
    <property name="scaledContents">
     <bool>false</bool>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
    <property name="wordWrap">
     <bool>false</bool>
    </property>
   </widget>
   <widget class="QLabel" name="meal_name_2">
    <property name="geometry">
     <rect>
      <x>1110</x>
      <y>433</y>
      <width>131</width>
      <height>31</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>12</pointsize>
      <weight>50</weight>
      <bold>false</bold>
      <underline>false</underline>
     </font>
    </property>
    <property name="layoutDirection">
     <enum>Qt::LeftToRight</enum>
    </property>
    <property name="text">
     <string>الوجبة:</string>
    </property>
    <property name="textFormat">
     <enum>Qt::PlainText</enum>
    </property>
    <property name="scaledContents">
     <bool>false</bool>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
    <property name="wordWrap">
     <bool>false</bool>
    </property>
   </widget>
   <widget class="QLabel" name="meal_name_3">
    <property name="geometry">
     <rect>
      <x>1130</x>
      <y>483</y>
      <width>91</width>
      <height>31</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>12</pointsize>
      <weight>50</weight>
      <bold>false</bold>
      <underline>false</underline>
     </font>
    </property>
    <property name="layoutDirection">
     <enum>Qt::LeftToRight</enum>
    </property>
    <property name="text">
     <string>العدد:</string>
    </property>
    <property name="textFormat">
     <enum>Qt::PlainText</enum>
    </property>
    <property name="scaledContents">
     <bool>false</bool>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
    <property name="wordWrap">
     <bool>false</bool>
    </property>
   </widget>
   <widget class="QLabel" name="message_2">
    <property name="geometry">
     <rect>
      <x>530</x>
      <y>162</y>
      <width>691</width>
      <height>71</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>14</pointsize>
      <weight>75</weight>
      <bold>true</bold>
      <underline>false</underline>
     </font>
    </property>
    <property name="layoutDirection">
     <enum>Qt::LeftToRight</enum>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(170, 255, 255);</string>
    </property>
    <property name="frameShape">
     <enum>QFrame::Panel</enum>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="textFormat">
     <enum>Qt::PlainText</enum>
    </property>
    <property name="scaledContents">
     <bool>false</bool>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
    <property name="wordWrap">
     <bool>false</bool>
    </property>
   </widget>
   <widget class="QPushButton" name="startbutton_acc">
    <property name="enabled">
     <bool>true</bool>
    </property>
    <property name="geometry">
     <rect>
      <x>830</x>
      <y>651</y>
      <width>141</width>
      <height>31</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>8</pointsize>
      <weight>75</weight>
      <bold>true</bold>
     </font>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(0, 85, 255);</string>
    </property>
    <property name="text">
     <string>تشغيل بوابة المدخل</string>
    </property>
    <property name="autoDefault">
     <bool>false</bool>
    </property>
   </widget>
   <widget class="QLabel" name="label_2">
    <property name="geometry">
     <rect>
      <x>330</x>
      <y>650</y>
      <width>151</width>
      <height>31</height>
     </rect>
    </property>
    <property name="text">
     <string>اختيار الطابعة الاساسية</string>
    </property>
   </widget>
   <widget class="QPushButton" name="startbutton_sor">
    <property name="enabled">
     <bool>true</bool>
    </property>
    <property name="geometry">
     <rect>
      <x>680</x>
      <y>651</y>
      <width>141</width>
      <height>31</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>8</pointsize>
      <weight>75</weight>
      <bold>true</bold>
     </font>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(255, 170, 255);</string>
    </property>
    <property name="text">
     <string>تشغيل بوابة المخرج</string>
    </property>
    <property name="autoDefault">
     <bool>false</bool>
    </property>
   </widget>
   <widget class="QLabel" name="message_3">
    <property name="geometry">
     <rect>
      <x>530</x>
      <y>0</y>
      <width>691</width>
      <height>51</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <family>Monotype Corsiva</family>
      <pointsize>15</pointsize>
      <weight>50</weight>
      <italic>true</italic>
      <bold>false</bold>
     </font>
    </property>
    <property name="layoutDirection">
     <enum>Qt::LeftToRight</enum>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(170, 255, 255);
font: italic 15pt &quot;Monotype Corsiva&quot;;</string>
    </property>
    <property name="frameShape">
     <enum>QFrame::Panel</enum>
    </property>
    <property name="text">
     <string>الديوان الوطني للخدمات الجامعية</string>
    </property>
    <property name="textFormat">
     <enum>Qt::PlainText</enum>
    </property>
    <property name="scaledContents">
     <bool>false</bool>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
    <property name="wordWrap">
     <bool>false</bool>
    </property>
   </widget>
   <widget class="QLabel" name="etabmsg">
    <property name="geometry">
     <rect>
      <x>530</x>
      <y>90</y>
      <width>691</width>
      <height>51</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <family>Albertus MT</family>
      <pointsize>16</pointsize>
      <weight>50</weight>
      <italic>false</italic>
      <bold>false</bold>
     </font>
    </property>
    <property name="layoutDirection">
     <enum>Qt::LeftToRight</enum>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(170, 255, 255);
font: 16pt &quot;Albertus MT&quot;;
</string>
    </property>
    <property name="frameShape">
     <enum>QFrame::Panel</enum>
    </property>
    <property name="text">
     <string> المؤسسة</string>
    </property>
    <property name="textFormat">
     <enum>Qt::PlainText</enum>
    </property>
    <property name="scaledContents">
     <bool>false</bool>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
    <property name="wordWrap">
     <bool>false</bool>
    </property>
   </widget>
   <widget class="QLabel" name="doumsg">
    <property name="geometry">
     <rect>
      <x>530</x>
      <y>40</y>
      <width>691</width>
      <height>51</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <family>Monotype Corsiva</family>
      <pointsize>15</pointsize>
      <weight>50</weight>
      <italic>true</italic>
      <bold>false</bold>
     </font>
    </property>
    <property name="layoutDirection">
     <enum>Qt::LeftToRight</enum>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(170, 255, 255);
font: italic 15pt &quot;Monotype Corsiva&quot;;</string>
    </property>
    <property name="text">
     <string>مديرية الخدمات الجامعية</string>
    </property>
    <property name="textFormat">
     <enum>Qt::PlainText</enum>
    </property>
    <property name="scaledContents">
     <bool>false</bool>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
    <property name="wordWrap">
     <bool>false</bool>
    </property>
   </widget>
   <widget class="QLabel" name="enter_lab">
    <property name="geometry">
     <rect>
      <x>790</x>
      <y>544</y>
      <width>321</width>
      <height>40</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>15</pointsize>
      <weight>75</weight>
      <bold>true</bold>
     </font>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(255, 255, 127);</string>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
   </widget>
   <widget class="QLabel" name="meal_name_4">
    <property name="geometry">
     <rect>
      <x>1130</x>
      <y>547</y>
      <width>91</width>
      <height>31</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>12</pointsize>
      <weight>50</weight>
      <bold>false</bold>
      <underline>false</underline>
     </font>
    </property>
    <property name="layoutDirection">
     <enum>Qt::LeftToRight</enum>
    </property>
    <property name="text">
     <string>الدخول</string>
    </property>
    <property name="textFormat">
     <enum>Qt::PlainText</enum>
    </property>
    <property name="scaledContents">
     <bool>false</bool>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
    <property name="wordWrap">
     <bool>false</bool>
    </property>
   </widget>
   <widget class="QLabel" name="meal_name_5">
    <property name="geometry">
     <rect>
      <x>1130</x>
      <y>601</y>
      <width>91</width>
      <height>31</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>12</pointsize>
      <weight>50</weight>
      <bold>false</bold>
      <underline>false</underline>
     </font>
    </property>
    <property name="layoutDirection">
     <enum>Qt::LeftToRight</enum>
    </property>
    <property name="text">
     <string>الخروج</string>
    </property>
    <property name="textFormat">
     <enum>Qt::PlainText</enum>
    </property>
    <property name="scaledContents">
     <bool>false</bool>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
    <property name="wordWrap">
     <bool>false</bool>
    </property>
   </widget>
   <widget class="QLabel" name="sort_lab">
    <property name="geometry">
     <rect>
      <x>790</x>
      <y>594</y>
      <width>321</width>
      <height>41</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>15</pointsize>
      <weight>75</weight>
      <bold>true</bold>
     </font>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(255, 255, 127);</string>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
   </widget>
   <widget class="QLabel" name="image_logo">
    <property name="geometry">
     <rect>
      <x>590</x>
      <y>478</y>
      <width>141</width>
      <height>141</height>
     </rect>
    </property>
    <property name="text">
     <string/>
    </property>
   </widget>
   <widget class="Line" name="line">
    <property name="geometry">
     <rect>
      <x>500</x>
      <y>-10</y>
      <width>16</width>
      <height>771</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">color: rgb(0, 85, 255);</string>
    </property>
    <property name="lineWidth">
     <number>5</number>
    </property>
    <property name="orientation">
     <enum>Qt::Vertical</enum>
    </property>
   </widget>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
